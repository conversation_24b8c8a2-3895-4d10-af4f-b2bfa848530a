# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/SensorNode/FrenetTL

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/SensorNode/FrenetTL/build

# Include any dependencies generated for this target.
include CMakeFiles/frenet_demo.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/frenet_demo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/frenet_demo.dir/flags.make

CMakeFiles/frenet_demo.dir/src/main.cpp.o: CMakeFiles/frenet_demo.dir/flags.make
CMakeFiles/frenet_demo.dir/src/main.cpp.o: ../src/main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/frenet_demo.dir/src/main.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/frenet_demo.dir/src/main.cpp.o -c /home/<USER>/Documents/SensorNode/FrenetTL/src/main.cpp

CMakeFiles/frenet_demo.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/frenet_demo.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/SensorNode/FrenetTL/src/main.cpp > CMakeFiles/frenet_demo.dir/src/main.cpp.i

CMakeFiles/frenet_demo.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/frenet_demo.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/SensorNode/FrenetTL/src/main.cpp -o CMakeFiles/frenet_demo.dir/src/main.cpp.s

CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o: CMakeFiles/frenet_demo.dir/flags.make
CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o: ../src/roundabout_lane.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o -c /home/<USER>/Documents/SensorNode/FrenetTL/src/roundabout_lane.cpp

CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/SensorNode/FrenetTL/src/roundabout_lane.cpp > CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.i

CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/SensorNode/FrenetTL/src/roundabout_lane.cpp -o CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.s

CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o: CMakeFiles/frenet_demo.dir/flags.make
CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o: ../src/frenet_optimal_trajectory.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o -c /home/<USER>/Documents/SensorNode/FrenetTL/src/frenet_optimal_trajectory.cpp

CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/SensorNode/FrenetTL/src/frenet_optimal_trajectory.cpp > CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.i

CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/SensorNode/FrenetTL/src/frenet_optimal_trajectory.cpp -o CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.s

CMakeFiles/frenet_demo.dir/src/utils.cpp.o: CMakeFiles/frenet_demo.dir/flags.make
CMakeFiles/frenet_demo.dir/src/utils.cpp.o: ../src/utils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/frenet_demo.dir/src/utils.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/frenet_demo.dir/src/utils.cpp.o -c /home/<USER>/Documents/SensorNode/FrenetTL/src/utils.cpp

CMakeFiles/frenet_demo.dir/src/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/frenet_demo.dir/src/utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/SensorNode/FrenetTL/src/utils.cpp > CMakeFiles/frenet_demo.dir/src/utils.cpp.i

CMakeFiles/frenet_demo.dir/src/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/frenet_demo.dir/src/utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/SensorNode/FrenetTL/src/utils.cpp -o CMakeFiles/frenet_demo.dir/src/utils.cpp.s

# Object files for target frenet_demo
frenet_demo_OBJECTS = \
"CMakeFiles/frenet_demo.dir/src/main.cpp.o" \
"CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o" \
"CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o" \
"CMakeFiles/frenet_demo.dir/src/utils.cpp.o"

# External object files for target frenet_demo
frenet_demo_EXTERNAL_OBJECTS =

frenet_demo: CMakeFiles/frenet_demo.dir/src/main.cpp.o
frenet_demo: CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o
frenet_demo: CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o
frenet_demo: CMakeFiles/frenet_demo.dir/src/utils.cpp.o
frenet_demo: CMakeFiles/frenet_demo.dir/build.make
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
frenet_demo: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
frenet_demo: CMakeFiles/frenet_demo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable frenet_demo"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/frenet_demo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/frenet_demo.dir/build: frenet_demo

.PHONY : CMakeFiles/frenet_demo.dir/build

CMakeFiles/frenet_demo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/frenet_demo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/frenet_demo.dir/clean

CMakeFiles/frenet_demo.dir/depend:
	cd /home/<USER>/Documents/SensorNode/FrenetTL/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/SensorNode/FrenetTL /home/<USER>/Documents/SensorNode/FrenetTL /home/<USER>/Documents/SensorNode/FrenetTL/build /home/<USER>/Documents/SensorNode/FrenetTL/build /home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles/frenet_demo.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/frenet_demo.dir/depend

