cmake_minimum_required(VERSION 3.10)
project(FrenetTL)

set(CMAKE_CXX_STANDARD 17)

# Add header directories
include_directories(
  /usr/include/eigen3
  include
)

# Find OpenCV
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

# Collect all source files
file(GLOB SOURCES src/*.cpp)

# Build
add_executable(frenet_demo
  src/main.cpp
  src/roundabout_lane.cpp
  src/frenet_optimal_trajectory.cpp
  src/utils.cpp
  # no cubic_spline_planner.cpp if it contains another main
)

target_include_directories(frenet_demo PRIVATE include)
target_link_libraries(frenet_demo ${OpenCV_LIBS})
