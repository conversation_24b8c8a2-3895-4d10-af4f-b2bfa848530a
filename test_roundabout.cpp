#include "FrenetTL/roundabout_lane.h"
#include <iostream>
#include <cmath>

int main() {
    try {
        // Test with simple angles
        float entrance_angle = 0.0f;  // East
        float exit_angle = M_PI_2;    // North
        float distance = 10.0f;
        
        std::cout << "Generating roundabout lane..." << std::endl;
        roundabout::PointList2 points = roundabout::GenerateRoundaboutLane(
            entrance_angle, exit_angle, distance);
        
        std::cout << "Generated " << points.size() << " points:" << std::endl;
        for (size_t i = 0; i < points.size(); ++i) {
            std::cout << "Point " << i << ": (" << points[i].x << ", " << points[i].y << ")" << std::endl;
        }
        
        std::cout << "Test completed successfully!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cout << "Error: " << e.what() << std::endl;
        return 1;
    }
}
