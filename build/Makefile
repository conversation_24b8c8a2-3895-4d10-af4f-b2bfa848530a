# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/SensorNode/FrenetTL

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/SensorNode/FrenetTL/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles /home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/SensorNode/FrenetTL/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named frenet_demo

# Build rule for target.
frenet_demo: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 frenet_demo
.PHONY : frenet_demo

# fast build rule for target.
frenet_demo/fast:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/build
.PHONY : frenet_demo/fast

src/frenet_optimal_trajectory.o: src/frenet_optimal_trajectory.cpp.o

.PHONY : src/frenet_optimal_trajectory.o

# target to build an object file
src/frenet_optimal_trajectory.cpp.o:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.o
.PHONY : src/frenet_optimal_trajectory.cpp.o

src/frenet_optimal_trajectory.i: src/frenet_optimal_trajectory.cpp.i

.PHONY : src/frenet_optimal_trajectory.i

# target to preprocess a source file
src/frenet_optimal_trajectory.cpp.i:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.i
.PHONY : src/frenet_optimal_trajectory.cpp.i

src/frenet_optimal_trajectory.s: src/frenet_optimal_trajectory.cpp.s

.PHONY : src/frenet_optimal_trajectory.s

# target to generate assembly for a file
src/frenet_optimal_trajectory.cpp.s:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/frenet_optimal_trajectory.cpp.s
.PHONY : src/frenet_optimal_trajectory.cpp.s

src/main.o: src/main.cpp.o

.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/roundabout_lane.o: src/roundabout_lane.cpp.o

.PHONY : src/roundabout_lane.o

# target to build an object file
src/roundabout_lane.cpp.o:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.o
.PHONY : src/roundabout_lane.cpp.o

src/roundabout_lane.i: src/roundabout_lane.cpp.i

.PHONY : src/roundabout_lane.i

# target to preprocess a source file
src/roundabout_lane.cpp.i:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.i
.PHONY : src/roundabout_lane.cpp.i

src/roundabout_lane.s: src/roundabout_lane.cpp.s

.PHONY : src/roundabout_lane.s

# target to generate assembly for a file
src/roundabout_lane.cpp.s:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/roundabout_lane.cpp.s
.PHONY : src/roundabout_lane.cpp.s

src/utils.o: src/utils.cpp.o

.PHONY : src/utils.o

# target to build an object file
src/utils.cpp.o:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/utils.cpp.o
.PHONY : src/utils.cpp.o

src/utils.i: src/utils.cpp.i

.PHONY : src/utils.i

# target to preprocess a source file
src/utils.cpp.i:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/utils.cpp.i
.PHONY : src/utils.cpp.i

src/utils.s: src/utils.cpp.s

.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.cpp.s:
	$(MAKE) -f CMakeFiles/frenet_demo.dir/build.make CMakeFiles/frenet_demo.dir/src/utils.cpp.s
.PHONY : src/utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... frenet_demo"
	@echo "... src/frenet_optimal_trajectory.o"
	@echo "... src/frenet_optimal_trajectory.i"
	@echo "... src/frenet_optimal_trajectory.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/roundabout_lane.o"
	@echo "... src/roundabout_lane.i"
	@echo "... src/roundabout_lane.s"
	@echo "... src/utils.o"
	@echo "... src/utils.i"
	@echo "... src/utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

