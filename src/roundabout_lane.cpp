#include "FrenetTL/roundabout_lane.h"
#include <cassert>

namespace roundabout {

PointList2 GenerateRoundaboutLane(float entrance_angle, float exit_angle,
                                  float distance_from_roundabout) {
  constexpr float kRoundaboutRadius = 12.0f;
  constexpr float kLaneHalfWidth = 2.5f;
  constexpr float kFarAway = 100.0f;
  constexpr size_t kNumPointsInArc = 10;
  constexpr size_t kNumPointsInRoundabout = 10;
  constexpr size_t kNumExitPoints = 3;
  constexpr float kMergeRadius = 5.0f;

  Point2 entry_arc_center(
      (kRoundaboutRadius + kMergeRadius) * std::cos(entrance_angle),
      (kRoundaboutRadius + kMergeRadius) * std::sin(entrance_angle));

  float first_entry_arc_point_angle = entrance_angle - M_PI_2;
  Point2 first_point_in_entry_arc =
      entry_arc_center +
      (kMergeRadius) * Point2(std::cos(first_entry_arc_point_angle),
                              std::sin(first_entry_arc_point_angle));

  PointList2 points = {
      first_point_in_entry_arc +
          distance_from_roundabout *
              Point2(std::cos(entrance_angle), std::sin(entrance_angle)),
      first_point_in_entry_arc};

  for (size_t ii = 1; ii <= kNumPointsInArc; ii++) {
    float angle = first_entry_arc_point_angle -
                  M_PI_2 * static_cast<float>(ii) / kNumPointsInArc;
    points.push_back(entry_arc_center +
                     kMergeRadius * Point2(std::cos(angle), std::sin(angle)));
  }

  Point2 first_point_on_roundabout(kRoundaboutRadius * std::cos(entrance_angle),
                                   kRoundaboutRadius * std::sin(entrance_angle));
  assert((points.back() - first_point_on_roundabout).norm() < 1e-3);

  for (size_t ii = 1; ii <= kNumPointsInRoundabout; ii++) {
    float next_angle = entrance_angle + (exit_angle - entrance_angle) *
                                         static_cast<float>(ii) / kNumPointsInRoundabout;
    points.emplace_back(kRoundaboutRadius * std::cos(next_angle),
                        kRoundaboutRadius * std::sin(next_angle));
  }

  // Create smooth exit arc similar to entrance
  Point2 exit_arc_center(
      (kRoundaboutRadius + kMergeRadius) * std::cos(exit_angle),
      (kRoundaboutRadius + kMergeRadius) * std::sin(exit_angle));

  Point2 last_point_on_roundabout(kRoundaboutRadius * std::cos(exit_angle),
                                  kRoundaboutRadius * std::sin(exit_angle));
  assert((points.back() - last_point_on_roundabout).norm() < 1e-3);


  float first_entry_arc_point_angle = entrance_angle - M_PI_2;
  Point2 first_point_in_entry_arc =
      entry_arc_center +
      (kMergeRadius) * Point2(std::cos(first_entry_arc_point_angle),
                              std::sin(first_entry_arc_point_angle));


  // Generate exit arc points
  float first_exit_arc_point_angle = exit_angle - M_PI_2;
  Point2 first_point_in_exit_arc =
      exit_arc_center +
      (kMergeRadius) * Point2(std::cos(first_exit_arc_point_angle),
                              std::sin(first_exit_arc_point_angle));


  for (size_t ii = 1; ii <= kNumPointsInArc; ii++) {
    float angle = first_exit_arc_point_angle -
                  M_PI_2 * static_cast<float>(ii) / kNumPointsInArc;
    points.push_back(exit_arc_center +
                     kMergeRadius * Point2(std::cos(angle), std::sin(angle)));
  }

  // Add final straight exit points
  Point2 last_point_in_exit_arc =
      exit_arc_center +
      kMergeRadius * Point2(std::cos(first_exit_arc_point_angle + M_PI_2),
                            std::sin(first_exit_arc_point_angle + M_PI_2));

  for (size_t i = 1; i <= kNumExitPoints; ++i) {
    float scale = distance_from_roundabout * static_cast<float>(i) / kNumExitPoints;
    points.push_back(last_point_in_exit_arc +
                     scale * Point2(std::cos(exit_angle), std::sin(exit_angle)));
  }

  return points;
}

}  // namespace roundabout
