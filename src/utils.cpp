// src/utils.cpp
#include "FrenetTL/utils.h"
#include <vector>
#include <numeric>

namespace cpprobotics {

std::vector<float> vec_diff(const std::vector<float>& input) {
  std::vector<float> output;
  for (size_t i = 1; i < input.size(); ++i) {
    output.push_back(input[i] - input[i - 1]);
  }
  return output;
}

std::vector<float> cum_sum(const std::vector<float>& input) {
  std::vector<float> output(input.size());
  std::partial_sum(input.begin(), input.end(), output.begin());
  return output;
}

}
