#pragma once

#include "frenet_path.h"
#include "cubic_spline.h"
#include <vector>
#include <opencv2/core/types.hpp>

namespace cpprobotics {

FrenetPath frenet_optimal_planning(
    Spline2D csp, float s0, float c_speed,
    float c_d, float c_d_d, float c_d_dd,
    std::vector<std::array<float, 2>> ob);

cv::Point2i cv_offset(
    float x, float y, int image_width = 2000, int image_height = 2000);

}
