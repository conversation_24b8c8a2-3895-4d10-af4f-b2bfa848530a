#include "FrenetTL/roundabout_lane.h"
#include "FrenetTL/cubic_spline.h"
#include "FrenetTL/frenet_path.h"
#include "FrenetTL/frenet_planner.h"
#include <opencv2/opencv.hpp>
#include <vector>
#include <cmath>
#include "FrenetTL/utils.h"

// ... other includes like quintic_polynomial.h, quartic_polynomial.h ...

using namespace cpprobotics;

#define SIM_LOOP 500

int main() {
  // Step 1: Generate roundabout centerline
  constexpr float kP1InitialDistanceToRoundabout = 10.0;  // m
  constexpr float kAngleOffset = M_PI_2 * 0.5;
  constexpr float kWedgeSize = M_PI;

  std::vector<float> angles = {
      kAngleOffset,
      kAngleOffset + 2.0 * M_PI / 4.0,
      kAngleOffset + 2.0 * 2.0 * M_PI / 4.0,
      kAngleOffset + 3.0 * 2.0 * M_PI / 4.0};

  // Use first lane as global reference
  roundabout::PointList2 ref_path =
      roundabout::GenerateRoundaboutLane(angles[0] + M_PI / 20, angles[0] + kWedgeSize- M_PI / 120,
                                         kP1InitialDistanceToRoundabout);

  // Step 2: Convert to (wx, wy) format
  Vec_f wx, wy;
  for (const auto& pt : ref_path) {
    wx.push_back(pt.x);
    wy.push_back(pt.y);
  }

  // Step 3: Build Spline2D reference
  Spline2D csp_obj(wx, wy);

std::cout << "=== Spline2D Debug Info ===" << std::endl;
std::cout << "Number of control points: " << wx.size() << std::endl;
std::cout << "Spline total arc length: " << csp_obj.s.back() << std::endl;

for (size_t i = 0; i < wx.size(); ++i) {
  std::cout << "  pt[" << i << "] = (" << wx[i] << ", " << wy[i] << ")" << std::endl;
}

// Optionally sample a few positions:
std::cout << "Sampled points from spline:" << std::endl;
for (float si = 0.0; si <= csp_obj.s.back(); si += 1.0) {
  auto pt = csp_obj.calc_postion(si);
  std::cout << "  s = " << si << " -> (x, y) = (" << pt[0] << ", " << pt[1] << ")" << std::endl;
}



  // Step 4: Setup Frenet planner initial state
  std::vector<Poi_f> obstcles = {};

  float c_speed = 2.0;  // m/s
  float c_d = 0.0;
  float c_d_d = 0.0;
  float c_d_dd = 0.0;
  float s0 = 0.0;

  cv::namedWindow("frenet", cv::WINDOW_NORMAL);



  for (int i = 0; i < SIM_LOOP; i++) {
    FrenetPath final_path = frenet_optimal_planning(
        csp_obj, s0, c_speed, c_d, c_d_d, c_d_dd, obstcles);


if (final_path.s.size() <= 1) {
  std::cerr << "[ERROR] Path is too short. final_path.s.size() = "
            << final_path.s.size() << std::endl;
  break;
}

    // Update state for next iteration
    s0 = final_path.s[1];
    c_d = final_path.d[1];
    c_d_d = final_path.d_d[1];
    c_d_dd = final_path.d_dd[1];
    c_speed = final_path.s_d[1];

    if ((std::pow(final_path.x[1] - wx.back(), 2) +
         std::pow(final_path.y[1] - wy.back(), 2)) <= 0.001) {
      break;
    }

    // Visualization

    
    cv::Mat bg(600, 800, CV_8UC3, cv::Scalar(255, 255, 255));
    for (size_t i = 1; i < wx.size(); i++) {
      cv::line(bg, cv_offset(wx[i - 1], wy[i - 1], bg.cols, bg.rows),
               cv_offset(wx[i], wy[i], bg.cols, bg.rows),
               cv::Scalar(0, 0, 0), 1);
    }

    for (size_t i = 0; i < final_path.x.size(); i++) {
      cv::circle(bg, cv_offset(final_path.x[i], final_path.y[i], bg.cols, bg.rows),
                 4, cv::Scalar(255, 0, 0), -1);
    }

    for (const auto& obs : obstcles) {
      cv::circle(bg, cv_offset(obs[0], obs[1], bg.cols, bg.rows), 4,
                 cv::Scalar(0, 0, 255), 5);
    }

cv::Mat display;
cv::resize(bg, display, cv::Size(), 0.5, 0.5);  // 50% scale, or tweak as needed
cv::imshow("frenet", display);
    cv::waitKey(5);
  }

  return 0;
}
