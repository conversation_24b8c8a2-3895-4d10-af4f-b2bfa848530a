#pragma once

#include <vector>
#include <cmath>

namespace roundabout {

struct Point2 {
  float x;
  float y;
  Point2(float x_val = 0, float y_val = 0) : x(x_val), y(y_val) {}
  Point2 operator+(const Point2& other) const {
    return Point2(x + other.x, y + other.y);
  }
  Point2 operator-(const Point2& other) const {
    return Point2(x - other.x, y - other.y);
  }
  Point2 operator*(float scalar) const {
    return Point2(x * scalar, y * scalar);
  }
  float norm() const {
    return std::sqrt(x * x + y * y);
  }
};

inline Point2 operator*(float scalar, const Point2& pt) {
  return Point2(pt.x * scalar, pt.y * scalar);
}

using PointList2 = std::vector<Point2>;

PointList2 GenerateRoundaboutLane(float entrance_angle, float exit_angle,
                                  float distance_from_roundabout);

}  // namespace roundabout
